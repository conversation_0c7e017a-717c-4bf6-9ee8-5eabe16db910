import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
// import BreakfastFood from '../views/BreakfastFood.vue'
// import LunchFood from '../views/LunchFood.vue'
// import DinnerFood from '../views/DinnerFood.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: '首頁',
      component: HomeView,
    },
    {
      path: '/breakfast',
      name: '早餐',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/BreakfastFood.vue'),
    },
    {
      path: '/lunch',
      name: '午餐',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/LunchFood.vue'),
    },
    {
      path: '/dinner',
      name: '晚餐',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/DinnerFood.vue'),
    }
  ],
})

export default router
