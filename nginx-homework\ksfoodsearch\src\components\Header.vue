<script setup lang="ts">
defineProps<{
  msg: string
}>()
</script>

<template>
  <div class="ct-header">
    <!-- 背景 -->
    <div class="bg">
      <div class="title">
        <h3>武汉金山软件园食堂</h3>
        <p>金山出品&nbsp;&nbsp;必属精品</p>
      </div>
      <div class="title-logo">
        <img src="https://zhweb.kingsoft.com/imgs/202210/1024159A75134CB9A048FA1352635773.png" alt="">
      </div>
    </div>
    <!-- 建筑物 -->
    <div class="building">
      <div>
        <img
          src="https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&amp;auto=format&amp;fit=crop&amp;w=2070&amp;q=80"
          alt="金山软件园建筑">
      </div>
    </div>
  </div>
</template>

<style scoped>
.bg {
  background-color: red;
  width: 400px;
  /* height: 400px; */
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  padding: 32px 24px;
  /* 文字缩进两个空格 */
  /* text-indent: 2em; */
}

.title-logo img {
  width: 130px;
  height: 80px;
}

.building {
  margin-top: -16px;
}

.building img {
  width: 360px;
  height: 200px;
}
</style>
