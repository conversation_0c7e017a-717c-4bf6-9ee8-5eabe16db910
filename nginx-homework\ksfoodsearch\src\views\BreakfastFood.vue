<script setup>
import { ref } from 'vue'
import Food from '../components/Food.vue'
import  axios  from "axios";
const FoodData = ref([])
// https://zhweb.kingsoft.com/mealMenu/detail/h5?canteenCode=WH_000&date=2025-07-08
const url = 'https://zhweb.kingsoft.com/mealMenu/detail/h5?canteenCode=WH_000&date=2025-07-08'
axios.get(url).then((response) => {
  console.log(response.data)
  FoodData.value = response.data.data 
})
</script>
<template>
    <Food :FoodData="FoodData"/>
</template>